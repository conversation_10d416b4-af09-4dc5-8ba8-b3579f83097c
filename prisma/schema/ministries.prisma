model ministries {
  id            String        @id @default(uuid()) @db.Uuid
  created_at    DateTime      @default(now())
  updated_at    DateTime      @updatedAt
  deleted_at    DateTime?
  name_th       String
  name_en       String?
  code          Int?
  short_name_th String?
  short_name_en String?
  projects      projects[]
  departments   departments[]
  divisions     divisions[]

  @@index([name_th])
}
