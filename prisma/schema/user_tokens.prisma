model user_tokens {
  id          String  @id @default(uuid()) @db.Uuid
  user_id     String  @db.Uuid
  token       String  @unique
  // Metadata
  ip_address  String?
  user_agent  String?
  device_info String?

  // Timestamps
  created_at DateTime @default(now())
  updated_at DateTime @default(now()) @updatedAt

  // Relations
  user users @relation(fields: [user_id], references: [id], onDelete: Cascade)

  // Indexes
  @@index([user_id])
  @@index([token])
}
