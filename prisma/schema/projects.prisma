model projects {
  id            String  @id @default(uuid()) @db.Uuid
  name          String
  contact_name  String?
  contact_phone String?
  contact_email String?
  budget        Float?
  ministry_id   String? @db.Uuid
  department_id String? @db.Uuid
  division_id   String? @db.Uuid

  created_at DateTime  @default(now())
  created_by_id String?   @db.Uuid
  updated_at DateTime  @default(now()) @updatedAt
  updated_by_id String?   @db.Uuid
  deleted_at DateTime?
  deleted_by_id String?   @db.Uuid

  department departments? @relation(fields: [department_id], references: [id], onDelete: SetNull)
  division   divisions?   @relation(fields: [division_id], references: [id], onDelete: SetNull)
  ministry   ministries?  @relation(fields: [ministry_id], references: [id], onDelete: SetNull)

  creator users? @relation("ProjectCreator", fields: [created_by_id], references: [id], onDelete: SetNull)
  updater users? @relation("ProjectUpdater", fields: [updated_by_id], references: [id], onDelete: SetNull)
  deleter users? @relation("ProjectDeleter", fields: [deleted_by_id], references: [id], onDelete: SetNull)

  @@index([name, ministry_id, department_id, division_id])
}
