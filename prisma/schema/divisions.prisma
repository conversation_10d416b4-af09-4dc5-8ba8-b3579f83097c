model divisions {
  id            String  @id @default(uuid()) @db.Uuid
  ministry_id   String  @db.Uuid
  department_id String  @db.Uuid
  name_th       String
  name_en       String?
  short_name_th String?
  short_name_en String?
  code          Int?

  created_at DateTime  @default(now())
  updated_at DateTime  @updatedAt
  deleted_at DateTime?

  ministry   ministries  @relation(fields: [ministry_id], references: [id], onDelete: Cascade)
  department departments @relation(fields: [department_id], references: [id], onDelete: Cascade)
  projects   projects[]

  @@index([ministry_id])
  @@index([department_id])
  @@index([name_th])
}
