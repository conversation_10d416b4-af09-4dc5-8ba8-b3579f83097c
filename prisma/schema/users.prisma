model users {
  id           String  @id @default(uuid()) @db.Uuid
  email        String  @unique
  password     String
  display_name String?

  created_at DateTime  @default(now())
  created_by String?   @db.Uuid
  updated_at DateTime  @default(now()) @updatedAt
  updated_by String?   @db.Uuid
  deleted_at DateTime?
  deleted_by String?   @db.Uuid

  // Relations
  access_tokens user_tokens[]

  // Self-referencing relations with proper naming
  createdBy users? @relation("UserCreatedBy", fields: [created_by], references: [id], onDelete: SetNull)
  updatedBy users? @relation("UserUpdatedBy", fields: [updated_by], references: [id], onDelete: SetNull)
  deletedBy users? @relation("UserDeletedBy", fields: [deleted_by], references: [id], onDelete: SetNull)

  // Reverse relations for self-referencing
  createdUsers users[] @relation("UserCreatedBy")
  updatedUsers users[] @relation("UserUpdatedBy")
  deletedUsers users[] @relation("UserDeletedBy")

  // Project relations
  createdProjects projects[] @relation("ProjectCreator")
  updatedProjects projects[] @relation("ProjectUpdater")
  deletedProjects projects[] @relation("ProjectDeleter")

  @@index([email])
}
