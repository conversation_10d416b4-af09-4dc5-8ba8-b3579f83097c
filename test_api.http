### Test Auth System

# Create a user
POST http://localhost:3000/users
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "display_name": "Test User"
}

###

# Login
POST http://localhost:3000/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

###

# Test Ministry CRUD (requires authentication token from login)
GET http://localhost:3000/ministries
Authorization: Bearer YOUR_TOKEN_HERE

###

# Create Ministry
POST http://localhost:3000/ministries
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN_HERE

{
  "name_th": "กระทรวงการคลัง",
  "name_en": "Ministry of Finance",
  "code": 1001,
  "short_name_th": "กค.",
  "short_name_en": "MOF"
}

###

# Get Ministry by ID
GET http://localhost:3000/ministries/MINISTRY_ID_HERE
Authorization: Bearer YOUR_TOKEN_HERE

###

# Update Ministry
PUT http://localhost:3000/ministries/MINISTRY_ID_HERE
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN_HERE

{
  "name_th": "กระทรวงการคลัง (แก้ไข)",
  "name_en": "Ministry of Finance (Updated)"
}

###

# Create Department
POST http://localhost:3000/departments
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN_HERE

{
  "ministry_id": "MINISTRY_ID_HERE",
  "name_th": "กรมบัญชีกลาง",
  "name_en": "Comptroller General's Department",
  "code": 2001,
  "short_name_th": "กบก.",
  "short_name_en": "CGD"
}

###

# Get Departments by Ministry
GET http://localhost:3000/departments?ministry_id=MINISTRY_ID_HERE
Authorization: Bearer YOUR_TOKEN_HERE

###

# Create Division
POST http://localhost:3000/divisions
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN_HERE

{
  "ministry_id": "MINISTRY_ID_HERE",
  "department_id": "DEPARTMENT_ID_HERE",
  "name_th": "กองบัญชีกลาง",
  "name_en": "Central Accounting Division",
  "code": 3001,
  "short_name_th": "กบก.",
  "short_name_en": "CAD"
}

###

# Get Divisions by Department
GET http://localhost:3000/divisions?department_id=DEPARTMENT_ID_HERE
Authorization: Bearer YOUR_TOKEN_HERE

###

# Logout
POST http://localhost:3000/auth/logout
Authorization: Bearer YOUR_TOKEN_HERE
