package services

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IDepartmentService interface {
	Create(input *DepartmentCreatePayload) (*models.Department, core.IError)
	Update(id string, input *DepartmentUpdatePayload) (*models.Department, core.IError)
	Find(id string) (*models.Department, core.IError)
	Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.Department], core.IError)
	Delete(id string) core.IError
	FindByMinistry(ministryID string, pageOptions *core.PageOptions) (*repository.Pagination[models.Department], core.IError)
}

type departmentService struct {
	ctx core.IContext
}

func (s departmentService) Create(input *DepartmentCreatePayload) (*models.Department, core.IError) {
	department := &models.Department{
		BaseModel:   models.NewBaseModel(),
		MinistryID:  input.MinistryID,
		NameTh:      input.NameTh,
		NameEn:      utils.ToPointer(input.NameEn),
		Code:        input.Code,
		ShortNameTh: utils.ToPointer(input.ShortNameTh),
		ShortNameEn: utils.ToPointer(input.ShortNameEn),
	}

	ierr := repo.Department(s.ctx).Create(department)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(department.ID)
}

func (s departmentService) Update(id string, input *DepartmentUpdatePayload) (*models.Department, core.IError) {
	department, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Update fields if provided
	if input.MinistryID != "" {
		department.MinistryID = input.MinistryID
	}
	if input.NameTh != "" {
		department.NameTh = input.NameTh
	}
	if input.NameEn != "" {
		department.NameEn = utils.ToPointer(input.NameEn)
	}
	if input.Code != nil {
		department.Code = input.Code
	}
	if input.ShortNameTh != "" {
		department.ShortNameTh = utils.ToPointer(input.ShortNameTh)
	}
	if input.ShortNameEn != "" {
		department.ShortNameEn = utils.ToPointer(input.ShortNameEn)
	}

	// Update timestamp
	department.UpdatedAt = utils.GetCurrentDateTime()

	ierr = repo.Department(s.ctx).Where("id = ?", id).Updates(department)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(department.ID)
}

func (s departmentService) Find(id string) (*models.Department, core.IError) {
	return repo.Department(s.ctx, repo.DepartmentWithMinistry()).FindOne("id = ?", id)
}

func (s departmentService) Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.Department], core.IError) {
	return repo.Department(s.ctx, repo.DepartmentOrderBy(pageOptions), repo.DepartmentWithMinistry()).Pagination(pageOptions)
}

func (s departmentService) FindByMinistry(ministryID string, pageOptions *core.PageOptions) (*repository.Pagination[models.Department], core.IError) {
	return repo.Department(s.ctx, repo.DepartmentByMinistry(ministryID), repo.DepartmentOrderBy(pageOptions), repo.DepartmentWithMinistry()).Pagination(pageOptions)
}

func (s departmentService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repo.Department(s.ctx).Delete("id = ?", id)
}

func NewDepartmentService(ctx core.IContext) IDepartmentService {
	return &departmentService{ctx: ctx}
}
