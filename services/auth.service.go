package services

import (
	"crypto/rand"
	"encoding/hex"
	"net/http"

	"gitlab.finema.co/finema/csp/csp-api/helpers"
	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/repo"
	core "gitlab.finema.co/finema/idin-core"
)

type IAuthService interface {
	Login(input *LoginPayload) (*LoginResponse, core.IError)
	Logout(token string) core.IError
	GenerateToken() (string, error)
	ValidateToken(token string) (*models.User, core.IError)
	CreateUserToken(userID, token, ipAddress, userAgent, deviceInfo string) core.IError
	DeleteUserToken(token string) core.IError
}

type authService struct {
	ctx core.IContext
}

type LoginPayload struct {
	Email      string
	Password   string
	IPAddress  string
	UserAgent  string
	DeviceInfo string
}

type SlackCallbackPayload struct {
	Code       string
	State      string
	IPAddress  string
	UserAgent  string
	DeviceInfo string
}

type LoginResponse struct {
	*models.User
	Token string `json:"token"`
}

func (s authService) Login(input *LoginPayload) (*LoginResponse, core.IError) {
	// Find user by email
	user, ierr := repo.User(s.ctx).FindOne("email = ?", input.Email)
	if ierr != nil {
		return nil, s.ctx.NewError(core.Error{
			Status:  http.StatusUnauthorized,
			Code:    "INVALID_CREDENTIALS",
			Message: "Invalid email or password",
		}, ierr)
	}

	// Check password
	if !helpers.CheckPasswordHash(input.Password, user.Password) {
		return nil, s.ctx.NewError(core.Error{
			Status:  http.StatusUnauthorized,
			Code:    "INVALID_CREDENTIALS",
			Message: "Invalid email or password",
		}, core.Error{})
	}

	// Generate token
	token, err := s.GenerateToken()
	if err != nil {
		return nil, s.ctx.NewError(core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "TOKEN_GENERATION_FAILED",
			Message: "Failed to generate token",
		}, core.Error{})
	}

	// Create user token record
	ierr = s.CreateUserToken(user.ID, token, input.IPAddress, input.UserAgent, input.DeviceInfo)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return &LoginResponse{
		User:  user,
		Token: token,
	}, nil
}

func (s authService) Logout(token string) core.IError {
	return s.DeleteUserToken(token)
}

func (s authService) GenerateToken() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

func (s authService) ValidateToken(token string) (*models.User, core.IError) {
	userToken, ierr := repo.UserToken(s.ctx).FindOne("token = ?", token)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	user, ierr := repo.User(s.ctx).FindOne("id = ?", userToken.UserID)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return user, nil
}

func (s authService) CreateUserToken(userID, token, ipAddress, userAgent, deviceInfo string) core.IError {
	userToken := &models.UserToken{
		BaseModelHardDelete: models.NewBaseModelHardDelete(),
		UserID:              userID,
		Token:               token,
		IPAddress:           ipAddress,
		UserAgent:           userAgent,
		DeviceInfo:          deviceInfo,
	}

	return repo.UserToken(s.ctx).Create(userToken)
}

func (s authService) DeleteUserToken(token string) core.IError {
	return repo.UserToken(s.ctx).Delete("token = ?", token)
}

func NewAuthService(ctx core.IContext) IAuthService {
	return &authService{ctx: ctx}
}
