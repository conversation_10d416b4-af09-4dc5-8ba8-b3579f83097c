package models

type Project struct {
	BaseModel
	Name         string   `json:"name" gorm:"column:name"`
	ContactName  *string  `json:"contact_name" gorm:"column:contact_name"`
	ContactPhone *string  `json:"contact_phone" gorm:"column:contact_phone"`
	ContactEmail *string  `json:"contact_email" gorm:"column:contact_email"`
	Budget       *float64 `json:"budget" gorm:"column:budget"`
	MinistryID   *string  `json:"ministry_id" gorm:"column:ministry_id"`
	DepartmentID *string  `json:"department_id" gorm:"column:department_id"`
	DivisionID   *string  `json:"division_id" gorm:"column:division_id"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id"`

	// Relations
	Ministry   *Ministry   `json:"ministry,omitempty" gorm:"foreignKey:MinistryID"`
	Department *Department `json:"department,omitempty" gorm:"foreignKey:DepartmentID"`
	Division   *Division   `json:"division,omitempty" gorm:"foreignKey:DivisionID"`

	CreatedBy *User `json:"created_by,omitempty" gorm:"foreignKey:CreatedByID"`
	UpdatedBy *User `json:"updated_by,omitempty" gorm:"foreignKey:UpdatedByID"`
	DeletedBy *User `json:"deleted_by,omitempty" gorm:"foreignKey:DeletedByID"`
}

func (Project) TableName() string {
	return "projects"
}
