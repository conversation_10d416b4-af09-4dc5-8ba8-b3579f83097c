package models

type Ministry struct {
	BaseModel
	NameTh      string  `json:"name_th" gorm:"column:name_th"`
	NameEn      *string `json:"name_en" gorm:"column:name_en"`
	Code        *int    `json:"code" gorm:"column:code"`
	ShortNameTh *string `json:"short_name_th" gorm:"column:short_name_th"`
	ShortNameEn *string `json:"short_name_en" gorm:"column:short_name_en"`

	// Relations
	Departments []Department `json:"departments,omitempty" gorm:"foreignKey:MinistryID"`
	Divisions   []Division   `json:"divisions,omitempty" gorm:"foreignKey:MinistryID"`
	Projects    []Project    `json:"projects,omitempty" gorm:"foreignKey:MinistryID"`
}

func (Ministry) TableName() string {
	return "ministries"
}
