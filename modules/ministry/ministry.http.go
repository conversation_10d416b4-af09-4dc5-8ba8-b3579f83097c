package ministry

import (
	"github.com/labstack/echo/v4"
	"gitlab.finema.co/finema/csp/csp-api/middleware"
	core "gitlab.finema.co/finema/idin-core"
)

func NewMinistryHTTP(e *echo.Echo) {
	ministry := &MinistryController{}
	
	// Ministry routes - all require authentication
	e.GET("/ministries", core.WithHTTPContext(ministry.Pagination), middleware.AuthMiddleware())
	e.GET("/ministries/:id", core.WithHTTPContext(ministry.Find), middleware.AuthMiddleware())
	e.POST("/ministries", core.WithHTTPContext(ministry.Create), middleware.AuthMiddleware())
	e.PUT("/ministries/:id", core.WithHTTPContext(ministry.Update), middleware.AuthMiddleware())
	e.DELETE("/ministries/:id", core.WithHTTPContext(ministry.Delete), middleware.AuthMiddleware())
}
