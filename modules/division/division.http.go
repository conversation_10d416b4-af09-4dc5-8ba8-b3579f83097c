package division

import (
	"github.com/labstack/echo/v4"
	"gitlab.finema.co/finema/csp/csp-api/middleware"
	core "gitlab.finema.co/finema/idin-core"
)

func NewDivisionHTTP(e *echo.Echo) {
	division := &DivisionController{}
	
	// Division routes - all require authentication
	e.GET("/divisions", core.WithHTTPContext(division.Pagination), middleware.AuthMiddleware())
	e.GET("/divisions/:id", core.WithHTTPContext(division.Find), middleware.AuthMiddleware())
	e.POST("/divisions", core.WithHTTPContext(division.Create), middleware.AuthMiddleware())
	e.PUT("/divisions/:id", core.WithHTTPContext(division.Update), middleware.AuthMiddleware())
	e.DELETE("/divisions/:id", core.WithHTTPContext(division.Delete), middleware.AuthMiddleware())
}
