{"name": "csp-api", "version": "1.0.0", "main": "index.js", "repository": "https://gitlab.finema.co/finema/csp/csp-api", "author": "<PERSON><PERSON>", "license": "MIT", "scripts": {"migrate": "prisma migrate dev", "studio": "prisma studio", "seed": "bun prisma/seed.ts"}, "dependencies": {"@prisma/client": "^6.13.0", "bcryptjs": "^3.0.2", "dotenv": "^17.2.1"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "prisma": "^6.13.0"}}