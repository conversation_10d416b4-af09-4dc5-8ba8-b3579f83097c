image: docker:stable
services:
  - docker:24.0.5-dind
variables:
  TAG_LATEST: $CI_REGISTRY_IMAGE:latest
  TAG_COMMIT: $CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA
  TAG_PROD_LATEST: $CI_REGISTRY_IMAGE:latest-prod
  TAG_PROD_COMMIT: $CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA-prod
  RELEASE_TOKEN: "**************************"
stages:
  - build
  - deploy

build-develop:
  stage: build
  only:
    - develop
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker build -t $TAG_COMMIT -t $TAG_LATEST .
    - docker push $TAG_COMMIT
    - docker push $TAG_LATEST
    - docker rmi $TAG_COMMIT $TAG_LATEST
  tags:
    - latest-runner
  environment:
    name: develop

deploy-develop:
  stage: deploy
  only:
    - develop
  image: alpine/git
  tags:
    - latest-runner
  before_script:
    - apk add --no-cache curl bash
    - curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh"  | bash
    - mv kustomize /usr/local/bin/
    - git config --global user.email "${GIT_USER_EMAIL:-$GITLAB_USER_EMAIL}"
    - git config --global user.name "${GIT_USER_NAME:-$GITLAB_USER_NAME}"
  script:
    - git clone https://gitlab-ci-token:${RELEASE_TOKEN}@${CI_SERVER_HOST}/finema/csp/csp-k8s.git
    - cd csp-k8s/overlays/develop
    - kustomize edit set image $TAG_LATEST=$TAG_COMMIT
    - cat kustomization.yml
    - git commit -am '[skip ci] image update'
    - git push


  environment:
    name: develop
