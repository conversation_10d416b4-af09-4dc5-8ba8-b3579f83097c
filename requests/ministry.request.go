package requests

import (
	core "gitlab.finema.co/finema/idin-core"
)

type MinistryCreate struct {
	core.BaseValidator
	NameTh *string `json:"name_th"`
	NameEn *string `json:"name_en"`
}

func (r *MinistryCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.Is<PERSON>tr<PERSON>equired(r.NameTh, "name_th"))

	return r.<PERSON>rror()
}

type MinistryUpdate struct {
	core.BaseValidator
	NameTh *string `json:"name_th"`
	NameEn *string `json:"name_en"`
}

func (r *MinistryUpdate) Valid(ctx core.IContext) core.IError {
	return r.Error()
}
