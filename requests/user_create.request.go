package requests

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type UserCreate struct {
	core.BaseValidator
	Email       *string `json:"email"`
	Password    *string `json:"password"`
	DisplayName *string `json:"display_name"`
}

func (r *UserCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsEmail(r.Email, "email"))
	r.Must(r.IsStrRequired(r.Email, "email"))
	r.Must(r.IsStrUnique(ctx, r.Email, models.User{}.TableName(), "email", "", "email"))

	r.Must(r.<PERSON>tr<PERSON>equired(r.Password, "password"))

	return r.Error()
}

type Login struct {
	core.BaseValidator
	Email    *string `json:"email"`
	Password *string `json:"password"`
}

func (r *Login) Valid(ctx core.IContext) core.IError {
	r.Must(r.<PERSON>mail(r.Email, "email"))
	r.Must(r.Is<PERSON>trRequired(r.<PERSON>ail, "email"))
	r.Must(r.Is<PERSON>trRequired(r.Password, "password"))

	return r.Error()
}
