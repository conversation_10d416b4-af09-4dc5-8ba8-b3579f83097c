package requests

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type DepartmentCreate struct {
	core.BaseValidator
	MinistryID *string `json:"ministry_id"`
	NameTh     *string `json:"name_th"`
	NameEn     *string `json:"name_en"`
}

func (r *DepartmentCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.MinistryID, "ministry_id"))
	r.Must(r.IsStrRequired(r.NameTh, "name_th"))

	// Check if ministry exists
	if r.MinistryID != nil {
		r.Must(r.IsExists(ctx, r.MinistryID, models.Ministry{}.TableName(), "id", "ministry_id"))
	}

	return r.Error()
}

type DepartmentUpdate struct {
	core.BaseValidator
	MinistryID *string `json:"ministry_id"`
	NameTh     *string `json:"name_th"`
	NameEn     *string `json:"name_en"`
}

func (r *DepartmentUpdate) Valid(ctx core.IContext) core.IError {
	// Check if ministry exists if provided
	if r.MinistryID != nil {
		r.Must(r.IsExists(ctx, r.MinistryID, models.Ministry{}.TableName(), "id", "ministry_id"))
	}

	return r.Error()
}
